import { Routes } from '@angular/router';
// import { authGuard } from '@core';

import { AdminLayout } from '@/layouts/admin-layout';
import { Dashboard } from '@/pages/dashboard';
// import { AuthLayoutComponent } from '@/layouts/auth-layout/auth-layout';
// import { LoginComponent } from '@/pages/sessions/login/login';
import { NoAuth, NotFound, ServerErr } from '@/pages/exceptions';

export const routes: Routes = [{
  path: '',
  component: AdminLayout,
  // canActivate: [authGuard],
  // canActivateChild: [authGuard],
  children: [{
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full'
  }, {
    path: 'dashboard',
    component: Dashboard,
    data: {
      title: 'Dashboard',
      titleI18n: 'dashboard',
    },
  }, {
    path: '403',
    component: NoAuth,
  }, {
    path: '404',
    component: NotFound,
  }, {
    path: '500',
    component: ServerErr,
  }]
// }, {
//   path: 'auth',
//   component: AuthLayout,
//   children: [{
//     path: 'login',
//     component: Login
//   }],
}, {
  path: '**',
  redirectTo: 'dashboard',
}];

import { Component, OnInit } from '@angular/core';
import { MatCardModule } from '@angular/material/card';

import { USER, SearchForm, TableColumns } from './data';

@Component({
  selector: 'app-user',
  templateUrl: './index.html',
  styleUrl: './index.scss',
  imports: [
    MatCardModule,
  ],
})
export class User implements OnInit {
  defaultColumns = [...TableColumns];
  tableData: any[] = [];
  total = 0;
  isLoading = false;

  query = {
    q: '',
    page: 1,
    limit: 10,
    sortby: 'created_at',
    order: 'desc'
  }

  constructor() { }

  ngOnInit(): void {
  }

}

import { Injectable, inject } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SettingsService } from './settings.service';

@Injectable({
  providedIn: 'root',
})
export class TranslateLangService {
  private readonly translate = inject(TranslateService);
  private readonly settings = inject(SettingsService);

  load() {
    return new Promise<void>(resolve => {
      const defaultLang = this.settings.getTranslateLang();
      console.log(`Attempting to load language: ${defaultLang}`);

      this.translate.setDefaultLang(defaultLang);
      this.translate.use(defaultLang).subscribe({
        next: translations => {
          console.log(`Successfully initialized '${defaultLang}' language.`, translations);
        },
        error: error => {
          console.error(`Problem with '${defaultLang}' language initialization.`, error);
        },
        complete: () => {
          console.log(`Language loading completed for: ${defaultLang}`);
          resolve();
        },
      });
    });
  }
}
